// app/admin/layout.tsx
import { ReactNode } from "react";
import { getServerSession } from "next-auth";
import { GET as authOptions } from "@/app/api/auth/[...nextauth]/route";
import { redirect } from "@/i18n/navigation";
import { Session } from "next-auth";

export default async function AdminLayout({
  children,
  params,
}: {
  children: ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const session: Session | null = await getServerSession(authOptions);

  if (!session?.user) {
    redirect({ href: "/admin/login", locale });
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow p-4">
        <h2 className="text-xl font-semibold">Admin Panel</h2>
      </header>
      <section className="p-6">{children}</section>
    </div>
  );
}
