// app/admin/layout.tsx
import { ReactNode } from "react";
import { getServerSession } from "next-auth";
import { GET as authOptions } from "@/app/api/auth/[...nextauth]/route";
import { redirect } from "next/navigation";
import { Session } from "next-auth";

export default async function AdminLayout({
  children,
}: {
  children: ReactNode;
}) {
  const session: Session | null = await getServerSession(authOptions);

  if (!session?.user) {
    redirect("/admin/login");
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <header className="bg-white shadow p-4">
        <h2 className="text-xl font-semibold">Admin Panel</h2>
      </header>
      <section className="p-6">{children}</section>
    </div>
  );
}
