import { useTranslations } from "next-intl";
import { getServerSession } from "next-auth";
import { GET as authOptions } from "@/app/api/auth/[...nextauth]/route";
import { redirect } from "next/navigation";
import { Session } from "next-auth";

export default async function AdminDashboardPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const session: Session | null = await getServerSession(authOptions);

  if (!session?.user) {
    redirect(`/${locale}/admin/login`);
  }

  const t = useTranslations("AdminDashboard");

  return (
    <div className="min-h-screen">
      <header className="p-4">
        <h2 className="text-xl font-semibold">Admin Panel</h2>
      </header>
      <main className="p-8">
        <h1 className="text-3xl font-bold mb-4">{t("title")}</h1>
        <p>{t("welcome")}</p>
      </main>
    </div>
  );
}
