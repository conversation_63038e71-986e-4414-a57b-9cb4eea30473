"use client";

import { signOut } from "next-auth/react";
import { useParams } from "next/navigation";
import { useState } from "react";
import { useTranslations } from "next-intl";

export default function LogoutButton() {
  const [isLoading, setIsLoading] = useState(false);
  const params = useParams();
  const locale = params.locale as string;
  const t = useTranslations("AdminDashboard");

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      await signOut({
        callbackUrl: `/${locale}/admin/login`,
        redirect: true,
      });
    } catch (error) {
      console.error("Logout error:", error);
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleLogout}
      disabled={isLoading}
      className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded disabled:bg-red-400 disabled:cursor-not-allowed flex items-center"
    >
      {isLoading ? (
        <>
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          {t("signingOut")}
        </>
      ) : (
        t("logout")
      )}
    </button>
  );
}
